"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalManager = void 0;
const BaseComponent_1 = require("../common/BaseComponent");
const TerminalInterface_1 = require("./TerminalInterface");
const MessageRenderer_1 = require("./MessageRenderer");
const InputHandler_1 = require("./InputHandler");
const ToolConfirmationHandler_1 = require("./ToolConfirmationHandler");
const prompts_1 = require("../../../core/prompts");
const uuid_1 = require("uuid");
class TerminalManager extends BaseComponent_1.BaseComponent {
    configManager;
    aiEngine;
    terminalInterface;
    messageRenderer;
    inputHandler;
    toolConfirmationHandler;
    systemPromptRegistry;
    currentSession = null;
    constructor(state, config, configManager, aiEngine) {
        super(state, config);
        this.configManager = configManager;
        this.aiEngine = aiEngine;
        this.terminalInterface = new TerminalInterface_1.TerminalInterface(state, config);
        this.messageRenderer = new MessageRenderer_1.MessageRenderer(state, config);
        this.inputHandler = new InputHandler_1.InputHandler(state, config);
        this.toolConfirmationHandler = new ToolConfirmationHandler_1.ToolConfirmationHandler(state, config);
        this.systemPromptRegistry = new prompts_1.SystemPromptRegistry({ configManager });
    }
    async render() {
        await this.initializeSession();
        await this.terminalInterface.render();
        await this.startConversationLoop();
    }
    async initializeSession() {
        this.currentSession = {
            id: (0, uuid_1.v4)(),
            startTime: new Date(),
            messages: [],
            provider: this.state.currentProvider,
            model: this.state.currentModel,
        };
        // Add welcome message
        const welcomeMessage = {
            id: (0, uuid_1.v4)(),
            role: 'system',
            content: this.createWelcomeMessage(),
            timestamp: new Date(),
        };
        this.currentSession.messages.push(welcomeMessage);
        await this.messageRenderer.renderMessage(welcomeMessage);
    }
    createWelcomeMessage() {
        const config = this.configManager.getConfig();
        const providerConfig = this.configManager.getProviderConfig();
        return `Welcome to AI CLI Terminal!

🤖 Provider: ${this.state.currentProvider}
📋 Model: ${this.state.currentModel}
🔧 Tools: ${this.getEnabledToolsCount()} enabled

Type your message or use these commands:
• /help - Show available commands
• /history - View conversation history
• /clear - Clear current conversation
• /switch - Switch AI provider
• /config - Open configuration
• /exit - Exit terminal

Ready to assist you!`;
    }
    getEnabledToolsCount() {
        const tools = this.configManager.getConfig().tools;
        let count = 0;
        if (tools.enableFileTools)
            count += 7; // File system tools
        if (tools.enableShellTools)
            count += 1; // Shell tool
        if (tools.enableWebTools)
            count += 2; // Web tools
        if (tools.enableMemoryTools)
            count += 1; // Memory tool
        return count;
    }
    async startConversationLoop() {
        while (true) {
            try {
                const userInput = await this.inputHandler.getInput();
                if (await this.handleCommand(userInput)) {
                    continue; // Command was handled, continue loop
                }
                // Process as regular message
                await this.processUserMessage(userInput);
            }
            catch (error) {
                if (error instanceof Error && error.message === 'EXIT_TERMINAL') {
                    break;
                }
                this.utils.showError(`Error: ${error}`);
                await this.utils.waitForKeyPress();
            }
        }
    }
    async handleCommand(input) {
        if (!input.startsWith('/')) {
            return false;
        }
        const [command, ...args] = input.slice(1).split(' ');
        switch (command.toLowerCase()) {
            case 'help':
                await this.showHelp();
                return true;
            case 'history':
                await this.showHistory();
                return true;
            case 'clear':
                await this.clearConversation();
                return true;
            case 'switch':
                await this.switchProvider();
                return true;
            case 'config':
                this.updateState({ currentView: 'config' });
                throw new Error('EXIT_TERMINAL');
            case 'prompts':
                await this.handlePromptCommand(args);
                return true;
            case 'exit':
                await this.exitTerminal();
                throw new Error('EXIT_TERMINAL');
            default:
                this.utils.showError(`Unknown command: /${command}`);
                await this.utils.waitForKeyPress();
                return true;
        }
    }
    async processUserMessage(input) {
        const userMessage = {
            id: (0, uuid_1.v4)(),
            role: 'user',
            content: input,
            timestamp: new Date(),
        };
        this.currentSession.messages.push(userMessage);
        await this.messageRenderer.renderMessage(userMessage);
        try {
            // Convert terminal messages to chat messages for AI engine
            const chatMessages = this.currentSession.messages
                .filter(msg => msg.role !== 'system') // Exclude system messages
                .map(msg => ({
                id: msg.id,
                role: msg.role,
                content: msg.content,
                timestamp: msg.timestamp,
                metadata: msg.metadata
            }));
            // Show typing indicator
            this.utils.startSpinner('AI is thinking...');
            // Process message with AI engine
            const aiResponse = await this.aiEngine.processMessage(chatMessages, {
                provider: this.state.currentProvider,
                model: this.state.currentModel
            });
            this.utils.stopSpinner(true);
            // Debug: Log response details
            if (!aiResponse.message.content || aiResponse.message.content.trim().length === 0) {
                console.log(this.utils.colorize('DEBUG: Received empty response from AI', this.config.theme.warning));
                console.log(this.utils.colorize(`Tool calls: ${aiResponse.toolCalls?.length || 0}`, this.config.theme.muted));
                console.log(this.utils.colorize(`Finish reason: ${aiResponse.finishReason || 'unknown'}`, this.config.theme.muted));
            }
            // Create assistant message
            const assistantMessage = {
                id: (0, uuid_1.v4)(),
                role: 'assistant',
                content: aiResponse.message.content,
                timestamp: new Date(),
                metadata: {
                    provider: this.state.currentProvider,
                    model: this.state.currentModel,
                    tokens: aiResponse.usage?.totalTokens,
                    toolCalls: aiResponse.toolCalls?.map(tc => ({
                        id: (0, uuid_1.v4)(), // Generate a new ID for display
                        name: tc.toolName,
                        parameters: {}, // ToolExecutionResult doesn't have parameters
                        status: tc.success ? 'completed' : 'failed',
                        result: tc.success ? tc.content : undefined,
                        error: tc.error,
                        timestamp: new Date(),
                        duration: tc.executionTime
                    }))
                }
            };
            this.currentSession.messages.push(assistantMessage);
            await this.messageRenderer.renderMessage(assistantMessage);
        }
        catch (error) {
            this.utils.stopSpinner(false);
            this.utils.showError(`AI processing failed: ${error instanceof Error ? error.message : String(error)}`);
            await this.utils.waitForKeyPress();
        }
    }
    async showHelp() {
        const helpText = `
🔧 Available Commands:

/help     - Show this help message
/history  - View conversation history
/clear    - Clear current conversation
/switch   - Switch AI provider
/config   - Open configuration menu
/prompts  - Manage system prompts
/exit     - Exit terminal

📝 System Prompt Commands:
/prompts list              - List all available system prompts
/prompts active            - Show currently active system prompt
/prompts set <id>          - Set active system prompt by ID
/prompts search <query>    - Search system prompts
/prompts info <id>         - Show detailed information about a prompt

🛠️ Tool Features:
• File operations (read, write, edit, search)
• Shell command execution
• Web content fetching and search
• Persistent memory management
• Model Context Protocol (MCP) support

💡 Tips:
• Use natural language to describe what you want to do
• The AI can use tools to help with file operations, web searches, etc.
• Tool execution requires confirmation unless disabled in preferences
`;
        console.log(this.utils.colorize(helpText, this.config.theme.info));
        await this.utils.waitForKeyPress();
    }
    async showHistory() {
        if (!this.currentSession || this.currentSession.messages.length <= 1) {
            this.utils.showInfo('No conversation history yet.');
            await this.utils.waitForKeyPress();
            return;
        }
        console.log(this.utils.formatHeader('📜 Conversation History'));
        console.log();
        for (const message of this.currentSession.messages) {
            if (message.role !== 'system') {
                await this.messageRenderer.renderMessage(message, true);
            }
        }
        await this.utils.waitForKeyPress();
    }
    async clearConversation() {
        const confirmed = await this.confirmAction('Clear current conversation?');
        if (confirmed) {
            await this.initializeSession();
            this.utils.showSuccess('Conversation cleared');
            await this.utils.waitForKeyPress();
        }
    }
    async switchProvider() {
        this.updateState({ currentView: 'auth' });
        throw new Error('EXIT_TERMINAL');
    }
    async exitTerminal() {
        // Save session to history
        if (this.currentSession && this.currentSession.messages.length > 1) {
            await this.configManager.addToHistory(this.currentSession);
        }
        this.utils.showInfo('Session saved. Goodbye! 👋');
        process.exit(0);
    }
    getCurrentSession() {
        return this.currentSession;
    }
    async addMessage(message) {
        if (this.currentSession) {
            this.currentSession.messages.push(message);
            await this.messageRenderer.renderMessage(message);
        }
    }
    async confirmToolExecution(toolName, parameters) {
        return await this.toolConfirmationHandler.confirmToolExecution(toolName, parameters);
    }
    async startNewConversation() {
        this.currentSession = {
            id: (0, uuid_1.v4)(),
            startTime: new Date(),
            messages: [],
            provider: this.state.currentProvider,
            model: this.state.currentModel,
        };
        // Add welcome message
        const welcomeMessage = {
            id: (0, uuid_1.v4)(),
            role: 'system',
            content: this.createWelcomeMessage(),
            timestamp: new Date(),
        };
        this.currentSession.messages.push(welcomeMessage);
        await this.messageRenderer.renderMessage(welcomeMessage);
    }
    async loadConversation(conversationId) {
        // In a real implementation, this would load the conversation from storage
        // For now, just start a new conversation
        await this.startNewConversation();
        this.updateState({ conversationId });
    }
    async handlePromptCommand(args) {
        if (args.length === 0) {
            await this.showPromptHelp();
            return;
        }
        const subCommand = args[0].toLowerCase();
        const restArgs = args.slice(1);
        try {
            await this.systemPromptRegistry.initialize();
            switch (subCommand) {
                case 'list':
                    await this.listSystemPrompts();
                    break;
                case 'active':
                    await this.showActivePrompt();
                    break;
                case 'set':
                    if (restArgs.length === 0) {
                        this.utils.showError('Please provide a prompt ID to set as active.');
                    }
                    else {
                        await this.setActivePrompt(restArgs[0]);
                    }
                    break;
                case 'search':
                    if (restArgs.length === 0) {
                        this.utils.showError('Please provide a search query.');
                    }
                    else {
                        await this.searchPrompts(restArgs.join(' '));
                    }
                    break;
                case 'info':
                    if (restArgs.length === 0) {
                        this.utils.showError('Please provide a prompt ID to show information for.');
                    }
                    else {
                        await this.showPromptInfo(restArgs[0]);
                    }
                    break;
                default:
                    this.utils.showError(`Unknown prompt command: ${subCommand}`);
                    await this.showPromptHelp();
            }
        }
        catch (error) {
            this.utils.showError(`Failed to execute prompt command: ${error instanceof Error ? error.message : String(error)}`);
        }
        await this.utils.waitForKeyPress();
    }
    async showPromptHelp() {
        const helpText = `
📝 System Prompt Management Commands:

/prompts list              - List all available system prompts
/prompts active            - Show currently active system prompt
/prompts set <id>          - Set active system prompt by ID
/prompts search <query>    - Search system prompts by name, description, or content
/prompts info <id>         - Show detailed information about a specific prompt

Examples:
  /prompts list
  /prompts set cli-agent
  /prompts search "software engineering"
  /prompts info my-custom-prompt
`;
        console.log(this.utils.colorize(helpText, this.config.theme.info));
    }
    async listSystemPrompts() {
        const prompts = this.systemPromptRegistry.getAllPrompts();
        const activePrompt = this.systemPromptRegistry.getActivePrompt();
        if (prompts.length === 0) {
            this.utils.showInfo('No system prompts available.');
            return;
        }
        let output = '\n📝 Available System Prompts:\n\n';
        prompts.forEach(prompt => {
            const isActive = activePrompt?.id === prompt.id;
            const activeIndicator = isActive ? ' ✓ (active)' : '';
            const category = prompt.category ? ` [${prompt.category}]` : '';
            output += `${this.utils.colorize(prompt.id, this.config.theme.primary)}${activeIndicator}${category}\n`;
            output += `  ${prompt.name}\n`;
            output += `  ${this.utils.colorize(prompt.description, this.config.theme.secondary)}\n\n`;
        });
        console.log(output);
    }
    async showActivePrompt() {
        const activePrompt = this.systemPromptRegistry.getActivePrompt();
        if (!activePrompt) {
            this.utils.showInfo('No active system prompt set.');
            return;
        }
        const output = `
📝 Active System Prompt:

${this.utils.colorize('ID:', this.config.theme.primary)} ${activePrompt.id}
${this.utils.colorize('Name:', this.config.theme.primary)} ${activePrompt.name}
${this.utils.colorize('Description:', this.config.theme.primary)} ${activePrompt.description}
${this.utils.colorize('Category:', this.config.theme.primary)} ${activePrompt.category || 'None'}
${this.utils.colorize('Created:', this.config.theme.primary)} ${activePrompt.createdAt.toLocaleDateString()}
${this.utils.colorize('Updated:', this.config.theme.primary)} ${activePrompt.updatedAt.toLocaleDateString()}

${this.utils.colorize('Content Preview:', this.config.theme.primary)}
${activePrompt.content.substring(0, 200)}${activePrompt.content.length > 200 ? '...' : ''}
`;
        console.log(output);
    }
    async setActivePrompt(promptId) {
        const success = await this.systemPromptRegistry.setActivePrompt(promptId);
        if (success) {
            const prompt = this.systemPromptRegistry.getPrompt(promptId);
            this.utils.showSuccess(`Active system prompt set to: ${prompt?.name} (${promptId})`);
        }
        else {
            this.utils.showError(`System prompt with ID '${promptId}' not found.`);
        }
    }
    async searchPrompts(query) {
        const results = this.systemPromptRegistry.searchPrompts(query);
        if (results.length === 0) {
            this.utils.showInfo(`No system prompts found matching: "${query}"`);
            return;
        }
        let output = `\n🔍 Search Results for "${query}":\n\n`;
        results.forEach(prompt => {
            const category = prompt.category ? ` [${prompt.category}]` : '';
            output += `${this.utils.colorize(prompt.id, this.config.theme.primary)}${category}\n`;
            output += `  ${prompt.name}\n`;
            output += `  ${this.utils.colorize(prompt.description, this.config.theme.secondary)}\n\n`;
        });
        console.log(output);
    }
    async showPromptInfo(promptId) {
        const prompt = this.systemPromptRegistry.getPrompt(promptId);
        if (!prompt) {
            this.utils.showError(`System prompt with ID '${promptId}' not found.`);
            return;
        }
        const isActive = this.systemPromptRegistry.getActivePrompt()?.id === prompt.id;
        const tags = prompt.tags ? prompt.tags.join(', ') : 'None';
        const output = `
📝 System Prompt Information:

${this.utils.colorize('ID:', this.config.theme.primary)} ${prompt.id}
${this.utils.colorize('Name:', this.config.theme.primary)} ${prompt.name}
${this.utils.colorize('Description:', this.config.theme.primary)} ${prompt.description}
${this.utils.colorize('Category:', this.config.theme.primary)} ${prompt.category || 'None'}
${this.utils.colorize('Tags:', this.config.theme.primary)} ${tags}
${this.utils.colorize('Status:', this.config.theme.primary)} ${isActive ? 'Active' : 'Inactive'}
${this.utils.colorize('Created:', this.config.theme.primary)} ${prompt.createdAt.toLocaleDateString()}
${this.utils.colorize('Updated:', this.config.theme.primary)} ${prompt.updatedAt.toLocaleDateString()}

${this.utils.colorize('Content:', this.config.theme.primary)}
${prompt.content}
`;
        console.log(output);
    }
}
exports.TerminalManager = TerminalManager;
//# sourceMappingURL=TerminalManager.js.map