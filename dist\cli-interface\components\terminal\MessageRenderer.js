"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageRenderer = void 0;
const BaseComponent_1 = require("../common/BaseComponent");
class MessageRenderer extends BaseComponent_1.BaseComponent {
    constructor(state, config) {
        super(state, config);
    }
    async render() {
        // This component is used inline, no standalone render needed
    }
    async renderMessage(message, compact = false) {
        const timestamp = this.formatTimestamp(message.timestamp);
        const roleIcon = this.getRoleIcon(message.role);
        const roleColor = this.getRoleColor(message.role);
        if (!compact) {
            console.log();
        }
        // Render message header
        const header = `${timestamp}${this.utils.colorize(roleIcon, roleColor)} ${this.utils.colorize(this.capitalizeRole(message.role), roleColor, true)}`;
        console.log(header);
        if (message.metadata?.tokens) {
            console.log(this.utils.colorize(`(${message.metadata.tokens} tokens)`, this.config.theme.muted));
        }
        // Render message content
        await this.renderContent(message.content, message.role);
        // Render tool calls if present
        if (message.metadata?.toolCalls) {
            await this.renderToolCalls(message.metadata.toolCalls);
        }
        if (!compact) {
            console.log();
        }
    }
    async renderContent(content, role) {
        if (role === 'system') {
            // Render system messages with special formatting
            console.log(this.utils.colorize(content, this.config.theme.muted));
            return;
        }
        // Handle empty content
        if (!content || content.trim().length === 0) {
            console.log(this.utils.colorize('(Empty response)', this.config.theme.muted));
            return;
        }
        // Check if content contains code blocks
        if (this.containsCodeBlocks(content)) {
            await this.renderContentWithCodeBlocks(content);
        }
        else {
            // Regular text content
            const lines = content.split('\n');
            for (const line of lines) {
                if (line.trim()) {
                    console.log(this.formatContentLine(line, role));
                }
                else {
                    console.log();
                }
            }
        }
    }
    async renderContentWithCodeBlocks(content) {
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        let lastIndex = 0;
        let match;
        while ((match = codeBlockRegex.exec(content)) !== null) {
            // Render text before code block
            const textBefore = content.slice(lastIndex, match.index);
            if (textBefore.trim()) {
                console.log(textBefore.trim());
            }
            // Render code block
            const language = match[1] || '';
            const code = match[2];
            console.log(this.utils.formatCodeBlock(code, language));
            lastIndex = match.index + match[0].length;
        }
        // Render remaining text
        const remainingText = content.slice(lastIndex);
        if (remainingText.trim()) {
            console.log(remainingText.trim());
        }
    }
    async renderToolCalls(toolCalls) {
        console.log();
        console.log(this.utils.colorize('🔧 Tool Calls:', this.config.theme.accent, true));
        for (const toolCall of toolCalls) {
            await this.renderToolCall(toolCall);
        }
    }
    async renderToolCall(toolCall) {
        const statusIcon = this.getToolStatusIcon(toolCall.status);
        const statusColor = this.getToolStatusColor(toolCall.status);
        console.log();
        console.log(`${this.utils.colorize(statusIcon, statusColor)} ${this.utils.colorize(toolCall.name, this.config.theme.accent)}`);
        if (toolCall.duration) {
            console.log(this.utils.colorize(`  ⏱️  ${this.utils.formatDuration(toolCall.duration)}`, this.config.theme.muted));
        }
        if (toolCall.error) {
            console.log(this.utils.colorize(`  ❌ ${toolCall.error}`, this.config.theme.error));
        }
        else if (toolCall.result && typeof toolCall.result === 'string') {
            const truncatedResult = this.utils.truncateText(toolCall.result, 200);
            console.log(this.utils.colorize(`  📄 ${truncatedResult}`, this.config.theme.muted));
        }
    }
    formatTimestamp(timestamp) {
        if (!this.config.showTimestamps) {
            return '';
        }
        const timeStr = timestamp.toLocaleTimeString();
        return this.utils.colorize(`[${timeStr}] `, this.config.theme.muted);
    }
    getRoleIcon(role) {
        const icons = {
            user: '👤',
            assistant: '🤖',
            system: '⚙️',
            tool: '🔧',
        };
        return icons[role] || '❓';
    }
    getRoleColor(role) {
        const colors = {
            user: this.config.theme.primary,
            assistant: this.config.theme.success,
            system: this.config.theme.muted,
            tool: this.config.theme.accent,
        };
        return colors[role] || this.config.theme.muted;
    }
    capitalizeRole(role) {
        return role.charAt(0).toUpperCase() + role.slice(1);
    }
    formatContentLine(line, role) {
        // Add subtle indentation for assistant messages
        if (role === 'assistant') {
            return `  ${line}`;
        }
        return line;
    }
    containsCodeBlocks(content) {
        return /```[\s\S]*?```/.test(content);
    }
    getToolStatusIcon(status) {
        const icons = {
            pending: '⏳',
            approved: '✅',
            denied: '❌',
            executing: '⚡',
            completed: '✅',
            failed: '❌',
        };
        return icons[status] || '❓';
    }
    getToolStatusColor(status) {
        const colors = {
            pending: this.config.theme.warning,
            approved: this.config.theme.success,
            denied: this.config.theme.error,
            executing: this.config.theme.accent,
            completed: this.config.theme.success,
            failed: this.config.theme.error,
        };
        return colors[status] || this.config.theme.muted;
    }
    async renderStreamingContent(content) {
        // For streaming responses, write character by character
        for (const char of content) {
            process.stdout.write(char);
            // Add small delay for visual effect
            await new Promise(resolve => setTimeout(resolve, 10));
        }
    }
    renderNewLine() {
        console.log();
    }
}
exports.MessageRenderer = MessageRenderer;
//# sourceMappingURL=MessageRenderer.js.map