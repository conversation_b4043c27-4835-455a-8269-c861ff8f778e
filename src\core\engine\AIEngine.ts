import { ChatMessage, BaseTool, ToolResult, ProviderClient } from '../types';
import { ProviderFactory } from '../providers/ProviderFactory';
import { ToolRegistry, ToolExecutionResult } from '../tools/base/ToolRegistry';
import { ToolExecutionEngine } from '../tools/base/ToolExecutionEngine';
import { ConfigManager } from '../config/ConfigManager';
import { SystemPromptRegistry } from '../prompts';

export interface AIEngineOptions {
  provider?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
  tools?: BaseTool[];
  streamResponse?: boolean;
}

export interface AIResponse {
  message: ChatMessage;
  toolCalls?: ToolExecutionResult[];
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason?: string;
}

export interface ToolConfirmationHandler {
  confirmExecution(toolName: string, parameters: Record<string, any>): Promise<boolean>;
  confirmToolExecution(toolName: string, parameters: Record<string, any>): Promise<boolean>;
}

export class AIEngine {
  private configManager: ConfigManager;
  private toolRegistry: ToolRegistry;
  private toolExecutionEngine: ToolExecutionEngine;
  private providerFactory: ProviderFactory;
  private systemPromptRegistry: SystemPromptRegistry;
  private toolConfirmationHandler?: ToolConfirmationHandler;

  constructor(
    configManager: ConfigManager,
    toolRegistry: ToolRegistry,
    toolConfirmationHandler?: ToolConfirmationHandler
  ) {
    this.configManager = configManager;
    this.toolRegistry = toolRegistry;
    this.toolExecutionEngine = new ToolExecutionEngine(toolRegistry, toolConfirmationHandler);
    this.providerFactory = new ProviderFactory();
    this.systemPromptRegistry = new SystemPromptRegistry({ configManager });
    this.toolConfirmationHandler = toolConfirmationHandler;
  }

  public setToolConfirmationHandler(handler: ToolConfirmationHandler): void {
    this.toolConfirmationHandler = handler;
    this.toolExecutionEngine.setConfirmationHandler(handler);
  }

  public async processMessage(
    messages: ChatMessage[],
    options: AIEngineOptions = {}
  ): Promise<AIResponse> {
    const provider = this.getProvider(options.provider);
    const availableTools = this.getAvailableTools(options.tools);

    try {
      // Get effective system prompt (from registry or options)
      const effectiveSystemPrompt = await this.getEffectiveSystemPrompt(options.systemPrompt);
      const messagesWithSystemPrompt = await this.injectSystemPrompt(messages, effectiveSystemPrompt);

      // Send initial message to AI provider
      const response = await provider.sendMessage(messagesWithSystemPrompt, availableTools, {
        model: options.model,
        temperature: options.temperature,
        maxTokens: options.maxTokens,
        stream: false
      });

      // Handle tool calls if present
      const toolResults: ToolExecutionResult[] = [];
      if (response.toolCalls && response.toolCalls.length > 0) {
        console.log(`AIEngine: Processing ${response.toolCalls.length} tool calls`);
        for (const toolCall of response.toolCalls) {
          console.log(`AIEngine: Executing tool call: ${toolCall.name} with params:`, toolCall.parameters);
          const result = await this.executeToolCall(toolCall);
          toolResults.push(result);
        }

        // If tools were executed, send results back to AI for final response
        if (toolResults.length > 0) {
          const toolMessages = this.createToolMessages(toolResults);
          const finalMessages = [...messagesWithSystemPrompt, response, ...toolMessages];

          const finalResponse = await provider.sendMessage(finalMessages, [], {
            model: options.model,
            temperature: options.temperature,
            maxTokens: options.maxTokens,
            stream: false
          });

          // Debug: Log if final response is empty
          if (!finalResponse.content || finalResponse.content.trim().length === 0) {
            console.warn('AIEngine: Final response after tool execution is empty');
            console.warn(`Tool results count: ${toolResults.length}`);
            console.warn(`Tool results: ${toolResults.map(r => `${r.toolName}: ${r.success ? 'success' : 'failed'}`).join(', ')}`);
          }

          return {
            message: finalResponse,
            toolCalls: toolResults,
            usage: finalResponse.metadata?.usage,
            finishReason: finalResponse.metadata?.finishReason
          };
        }
      }

      return {
        message: response,
        toolCalls: toolResults,
        usage: response.metadata?.usage,
        finishReason: response.metadata?.finishReason
      };
    } catch (error) {
      throw new Error(`AI Engine processing failed: ${error}`);
    }
  }

  public async *streamMessage(
    messages: ChatMessage[],
    options: AIEngineOptions = {}
  ): AsyncGenerator<string, AIResponse> {
    const provider = this.getProvider(options.provider);
    const availableTools = this.getAvailableTools(options.tools);

    if (!provider.streamMessage) {
      throw new Error('Provider does not support streaming');
    }

    try {
      let fullContent = '';
      const generator = provider.streamMessage(messages, availableTools, {
        model: options.model,
        temperature: options.temperature,
        maxTokens: options.maxTokens,
        stream: true
      });

      let finalMessage: ChatMessage;
      
      for await (const chunk of generator) {
        if (typeof chunk === 'string') {
          fullContent += chunk;
          yield chunk;
        } else {
          finalMessage = chunk;
        }
      }

      // Handle tool calls if present
      const toolResults: ToolExecutionResult[] = [];
      if (finalMessage!.toolCalls && finalMessage!.toolCalls.length > 0) {
        for (const toolCall of finalMessage!.toolCalls) {
          const result = await this.executeToolCall(toolCall);
          toolResults.push(result);
        }

        // If tools were executed, send results back to AI for final response
        if (toolResults.length > 0) {
          const toolMessages = this.createToolMessages(toolResults);
          const finalMessages = [...messages, finalMessage!, ...toolMessages];

          const finalResponse = await provider.sendMessage(finalMessages, [], {
            model: options.model,
            temperature: options.temperature,
            maxTokens: options.maxTokens,
            stream: false
          });

          // Debug: Log if final response is empty
          if (!finalResponse.content || finalResponse.content.trim().length === 0) {
            console.warn('AIEngine (stream): Final response after tool execution is empty');
            console.warn(`Tool results count: ${toolResults.length}`);
            console.warn(`Tool results: ${toolResults.map(r => `${r.toolName}: ${r.success ? 'success' : 'failed'}`).join(', ')}`);
          }

          return {
            message: finalResponse,
            toolCalls: toolResults,
            usage: finalResponse.metadata?.usage,
            finishReason: finalResponse.metadata?.finishReason
          };
        }
      }

      return {
        message: finalMessage!,
        toolCalls: toolResults,
        usage: finalMessage!.metadata?.usage,
        finishReason: finalMessage!.metadata?.finishReason
      };
    } catch (error) {
      throw new Error(`AI Engine streaming failed: ${error}`);
    }
  }

  private getProvider(providerName?: string): ProviderClient {
    const name = providerName || this.configManager.getDefaultProvider();
    const config = this.configManager.getProviderConfigForClient(name);
    return ProviderFactory.createProvider(name, config);
  }

  private getAvailableTools(requestedTools?: any[]): any[] {
    if (requestedTools) {
      return requestedTools;
    }

    // Get all enabled tools from registry
    const allTools = this.toolRegistry.getAllTools();
    const filteredTools = allTools.filter(tool => {
      const category = tool.getCategory();
      return this.configManager.isToolCategoryEnabled(category);
    });

    // Debug: Log available tools
    console.log(`AIEngine: Available tools count: ${filteredTools.length}`);
    console.log(`AIEngine: Available tools: ${filteredTools.map(t => t.name).join(', ')}`);

    return filteredTools;
  }

  private async executeToolCall(toolCall: any): Promise<ToolExecutionResult> {
    try {
      const result = await this.toolExecutionEngine.executeSingleTool(
        toolCall.name,
        toolCall.parameters,
        {
          requireConfirmation: this.configManager.requiresToolConfirmation(),
          timeout: 30000,
          retries: 0
        }
      );

      return result;
    } catch (error) {
      return {
        success: false,
        content: '',
        error: `Tool execution failed: ${error}`,
        toolName: toolCall.name,
        executionTime: 0,
        context: {}
      };
    }
  }

  private createToolMessages(toolResults: ToolExecutionResult[]): ChatMessage[] {
    return toolResults.map(result => ({
      id: this.generateId(),
      role: 'tool' as const,
      content: result.success ? result.content : result.error || 'Tool execution failed',
      timestamp: new Date(),
      metadata: {
        toolCallId: result.toolName,
        toolResult: result
      }
    }));
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  // Utility methods
  public async testProvider(providerName?: string): Promise<boolean> {
    try {
      const provider = this.getProvider(providerName);
      return await provider.testConnection();
    } catch (error) {
      return false;
    }
  }

  public async getAvailableModels(providerName?: string): Promise<string[]> {
    try {
      const provider = this.getProvider(providerName);
      return await provider.getAvailableModels();
    } catch (error) {
      return [];
    }
  }

  public getRegisteredTools(): any[] {
    return this.toolRegistry.getAllTools();
  }

  public getToolsByCategory(category: string): any[] {
    return this.toolRegistry.getToolsByCategory(category);
  }

  public isToolEnabled(toolName: string): boolean {
    return this.toolRegistry.isToolEnabled(toolName);
  }

  public enableTool(toolName: string): void {
    this.toolRegistry.enableTool(toolName);
  }

  public disableTool(toolName: string): void {
    this.toolRegistry.disableTool(toolName);
  }

  private async getEffectiveSystemPrompt(optionsSystemPrompt?: string): Promise<string | undefined> {
    // Priority: options.systemPrompt > active system prompt from registry > provider default
    if (optionsSystemPrompt) {
      return optionsSystemPrompt;
    }

    try {
      await this.systemPromptRegistry.initialize();
      const activePrompt = this.systemPromptRegistry.getActivePrompt();
      return activePrompt?.content;
    } catch (error) {
      console.warn('Failed to get active system prompt from registry:', error);
      return undefined;
    }
  }

  private async injectSystemPrompt(messages: ChatMessage[], systemPrompt?: string): Promise<ChatMessage[]> {
    if (!systemPrompt) {
      return messages;
    }

    // Check if there's already a system message
    const hasSystemMessage = messages.length > 0 && messages[0].role === 'system';

    if (hasSystemMessage) {
      // Replace existing system message
      return [
        {
          id: `system-${Date.now()}`,
          role: 'system',
          content: systemPrompt,
          timestamp: new Date()
        },
        ...messages.slice(1)
      ];
    } else {
      // Add system message at the beginning
      return [
        {
          id: `system-${Date.now()}`,
          role: 'system',
          content: systemPrompt,
          timestamp: new Date()
        },
        ...messages
      ];
    }
  }

  public getSystemPromptRegistry(): SystemPromptRegistry {
    return this.systemPromptRegistry;
  }
}
