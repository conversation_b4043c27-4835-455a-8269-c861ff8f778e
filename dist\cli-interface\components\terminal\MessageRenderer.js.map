{"version": 3, "file": "MessageRenderer.js", "sourceRoot": "", "sources": ["../../../../src/cli-interface/components/terminal/MessageRenderer.ts"], "names": [], "mappings": ";;;AAAA,2DAAwD;AAGxD,MAAa,eAAgB,SAAQ,6BAAa;IAChD,YAAY,KAAe,EAAE,MAAiB;QAC5C,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACvB,CAAC;IAEM,KAAK,CAAC,MAAM;QACjB,6DAA6D;IAC/D,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,OAAwB,EAAE,UAAmB,KAAK;QAC3E,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAElD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC;QAED,wBAAwB;QACxB,MAAM,MAAM,GAAG,GAAG,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE,CAAC;QACpJ,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEpB,IAAI,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;YAC7B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACnG,CAAC;QAED,yBAAyB;QACzB,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QAExD,+BAA+B;QAC/B,IAAI,OAAO,CAAC,QAAQ,EAAE,SAAS,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACzD,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,CAAC,GAAG,EAAE,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAe,EAAE,IAAY;QACvD,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;YACtB,iDAAiD;YACjD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YACnE,OAAO;QACT,CAAC;QAED,uBAAuB;QACvB,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9E,OAAO;QACT,CAAC;QAED,wCAAwC;QACxC,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,uBAAuB;YACvB,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAClC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;oBAChB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;gBAClD,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,GAAG,EAAE,CAAC;gBAChB,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,OAAe;QACvD,MAAM,cAAc,GAAG,2BAA2B,CAAC;QACnD,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,KAAK,CAAC;QAEV,OAAO,CAAC,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACvD,gCAAgC;YAChC,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;YACzD,IAAI,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;YACjC,CAAC;YAED,oBAAoB;YACpB,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAChC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;YAExD,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC5C,CAAC;QAED,wBAAwB;QACxB,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,SAAgB;QAC5C,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;QAEnF,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAa;QACxC,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAE7D,OAAO,CAAC,GAAG,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAE/H,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACrH,CAAC;QAED,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YACnB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACrF,CAAC;aAAM,IAAI,QAAQ,CAAC,MAAM,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAClE,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;YACtE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,eAAe,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,SAAe;QACrC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAChC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,OAAO,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC;QAC/C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,OAAO,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACvE,CAAC;IAEO,WAAW,CAAC,IAAY;QAC9B,MAAM,KAAK,GAAG;YACZ,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,IAAI;YACf,MAAM,EAAE,IAAI;YACZ,IAAI,EAAE,IAAI;SACX,CAAC;QACF,OAAO,KAAK,CAAC,IAA0B,CAAC,IAAI,GAAG,CAAC;IAClD,CAAC;IAEO,YAAY,CAAC,IAAY;QAC/B,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO;YAC/B,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO;YACpC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK;YAC/B,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;SAC/B,CAAC;QACF,OAAO,MAAM,CAAC,IAA2B,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;IACxE,CAAC;IAEO,cAAc,CAAC,IAAY;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACtD,CAAC;IAEO,iBAAiB,CAAC,IAAY,EAAE,IAAY;QAClD,gDAAgD;QAChD,IAAI,IAAI,KAAK,WAAW,EAAE,CAAC;YACzB,OAAO,KAAK,IAAI,EAAE,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,kBAAkB,CAAC,OAAe;QACxC,OAAO,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAEO,iBAAiB,CAAC,MAAc;QACtC,MAAM,KAAK,GAAG;YACZ,OAAO,EAAE,GAAG;YACZ,QAAQ,EAAE,GAAG;YACb,MAAM,EAAE,GAAG;YACX,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,GAAG;YACd,MAAM,EAAE,GAAG;SACZ,CAAC;QACF,OAAO,KAAK,CAAC,MAA4B,CAAC,IAAI,GAAG,CAAC;IACpD,CAAC;IAEO,kBAAkB,CAAC,MAAc;QACvC,MAAM,MAAM,GAAG;YACb,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO;YAClC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO;YACnC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK;YAC/B,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;YACnC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO;YACpC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK;SAChC,CAAC;QACF,OAAO,MAAM,CAAC,MAA6B,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;IAC1E,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,OAAe;QACjD,wDAAwD;QACxD,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;YAC3B,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC3B,oCAAoC;YACpC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAEM,aAAa;QAClB,OAAO,CAAC,GAAG,EAAE,CAAC;IAChB,CAAC;CACF;AA5MD,0CA4MC"}